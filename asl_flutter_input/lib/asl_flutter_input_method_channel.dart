import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'asl_flutter_input_platform_interface.dart';

/// An implementation of [AslFlutterInputPlatform] that uses method channels.
class MethodChannelAslFlutterInput extends AslFlutterInputPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('asl_flutter_input');

  @override
  Future<String?> getPlatformVersion() async {
    final version =
        await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }

  @override
  Future<String> setCurrentLanguage(String languageCode) async {
    Map<String, String> params = {'languageCode': languageCode};
    String result = await methodChannel.invokeMethod<String>(
            'setCurrentLanguage', params) ??
        'Unable to set new language';
    return result;
  }

  @override
  Future<String> setAPIKey(String apiKey) async {
    Map<String, String> params = {'apiKey': apiKey};
    String result =
        await methodChannel.invokeMethod<String>('setAPIKey', params) ??
            'Unable to save new key';
    return result;
  }

  @override
  initAudioTranscription() async {
    await methodChannel.invokeMethod<String>('initAudioTranscription');
  }

  @override
  Future<String> fetchTranscribedText() async {
    String result =
        await methodChannel.invokeMethod<String>('fetchTranscribedText') ??
            'Unable to fetch result';
    return result;
  }

  @override
  stopListening() async {
    await methodChannel.invokeMethod<String>('stopListening') ??
        'Unable to fetch result';
  }
}
