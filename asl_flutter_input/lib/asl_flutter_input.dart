import 'asl_flutter_input_platform_interface.dart';

class AslFlutterInput {
  Future<String> setCurrentLanguage(String languageCode) {
    return AslFlutterInputPlatform.instance.setCurrentLanguage(languageCode);
  }

  Future<String> setAPIKey(String apiKey) async {
    return AslFlutterInputPlatform.instance.setAPIKey(apiKey);
  }

  initAudioTranscription() {
    return AslFlutterInputPlatform.instance.initAudioTranscription();
  }

  Future<String> fetchTranscribedText() {
    return AslFlutterInputPlatform.instance.fetchTranscribedText();
  }

  stopListening() {
    return AslFlutterInputPlatform.instance.stopListening();
  }
}
