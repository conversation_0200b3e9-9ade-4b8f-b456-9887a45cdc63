plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.example.asl_flutter_input_example"
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.example.asl_flutter_input_example"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
        }
    }

    packagingOptions {
        pickFirst '**/META-INF/DEPENDENCIES'
        pickFirst '**/META-INF/LICENSE'
        pickFirst '**/META-INF/LICENSE.txt'
        pickFirst '**/META-INF/NOTICE'
        pickFirst '**/META-INF/NOTICE.txt'
        pickFirst '**/META-INF/ASL2.0'
        pickFirst '**/META-INF/LGPL2.1'
        pickFirst '**/META-INF/INDEX.LIST'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/LGPL2.1'
        exclude 'META-INF/INDEX.LIST'
    }
}

flutter {
    source '../..'
}

dependencies {
    // Include the AAR file that contains CloudSpeechSessionFactory
    implementation fileTree(dir: 'libs', include: ['*.aar'])

    // Google Cloud Speech API dependencies - matching AAR file requirements
    implementation 'com.google.cloud:google-cloud-speech:4.69.0'
    implementation 'com.google.api.grpc:grpc-google-cloud-speech-v1p1beta1:2.69.0'

    // Google API GAX libraries (contains ResponseObserver)
    implementation 'com.google.api:gax:2.70.1'
    implementation 'com.google.api:gax-grpc:2.70.1'

    // Google API common libraries
    implementation 'com.google.api:api-common:2.53.1'
    implementation 'com.google.auth:google-auth-library-credentials:1.39.0'
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.39.0'

    // Protocol Buffers - matching AAR file requirements (contains GeneratedMessageV3)
    implementation 'com.google.protobuf:protobuf-java:3.25.8'
    implementation 'com.google.protobuf:protobuf-java-util:3.25.8'
    implementation 'com.google.api.grpc:proto-google-common-protos:2.61.1'

    // gRPC dependencies - newer versions compatible with protobuf 3.25.8
    implementation 'io.grpc:grpc-okhttp:1.71.0'
    implementation 'io.grpc:grpc-protobuf:1.71.0'
    implementation 'io.grpc:grpc-stub:1.71.0'
    implementation 'io.grpc:grpc-auth:1.71.0'
    implementation 'io.grpc:grpc-netty-shaded:1.71.0'

    // Logging
    implementation 'com.google.flogger:flogger:0.8'
    implementation 'com.google.flogger:flogger-system-backend:0.8'

    // Other dependencies
    implementation 'joda-time:joda-time:2.12.7'
}
