import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:asl_flutter_input/asl_flutter_input_method_channel.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  MethodChannelAslFlutterInput platform = MethodChannelAslFlutterInput();
  const MethodChannel channel = MethodChannel('asl_flutter_input');

  setUp(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        return '42';
      },
    );
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(channel, null);
  });

  test('getPlatformVersion', () async {
    expect(await platform.getPlatformVersion(), '42');
  });
}
