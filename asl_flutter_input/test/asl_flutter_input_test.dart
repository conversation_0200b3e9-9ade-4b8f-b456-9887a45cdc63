import 'package:flutter_test/flutter_test.dart';
import 'package:asl_flutter_input/asl_flutter_input.dart';
import 'package:asl_flutter_input/asl_flutter_input_platform_interface.dart';
import 'package:asl_flutter_input/asl_flutter_input_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockAslFlutterInputPlatform
    with MockPlatformInterfaceMixin
    implements AslFlutterInputPlatform {
  @override
  Future<String?> getPlatformVersion() => Future.value('42');

  @override
  Future<String> fetchTranscribedText() {
    // TODO: implement fetchTranscribedText
    throw UnimplementedError();
  }

  @override
  initAudioTranscription(String projectId, String serviceAccountJson) {
    // TODO: implement initAudioTranscription
    throw UnimplementedError();
  }

  @override
  Future<String> setAPIKey(String apiKey) {
    // TODO: implement setAPIKey
    throw UnimplementedError();
  }

  @override
  Future<String> setCurrentLanguage(String languageCode) {
    // TODO: implement setCurrentLanguage
    throw UnimplementedError();
  }

  @override
  stopListening() {
    // TODO: implement stopListening
    throw UnimplementedError();
  }
}

void main() {
  final AslFlutterInputPlatform initialPlatform =
      AslFlutterInputPlatform.instance;

  test('$MethodChannelAslFlutterInput is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelAslFlutterInput>());
  });
}
