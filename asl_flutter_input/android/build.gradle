group 'com.example.asl_flutter_input'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.7.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs 'libs'
        }
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    if (project.android.hasProperty("namespace")) {
        namespace 'com.example.asl_flutter_input'
    }

    compileSdk 33

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        test.java.srcDirs += 'src/test/kotlin'
    }

    defaultConfig {
        minSdkVersion 21
    }

    // Force resolution of conflicting dependencies
    configurations.all {
        resolutionStrategy {
            force 'commons-codec:commons-codec:1.15'
            force 'com.google.guava:guava:31.1-android'
            force 'com.google.errorprone:error_prone_annotations:2.18.0'
            force 'io.grpc:grpc-okhttp:1.71.0'
            force 'io.grpc:grpc-core:1.71.0'
            force 'io.grpc:grpc-stub:1.71.0'
            force 'com.google.auth:google-auth-library-oauth2-http:1.39.0'
            force 'com.google.auth:google-auth-library-credentials:1.39.0'
            force 'com.google.auth:google-auth-library-appengine:1.39.0'
        }
    }

    dependencies {
        implementation 'androidx.annotation:annotation:1.3.0'
        
        // Include the .aar file
        implementation files('libs/asl_v1.0.4.aar')
        
        // Required dependencies that are NOT included in the .aar
        implementation('com.google.cloud:google-cloud-speech:4.69.0') {
            exclude group: 'commons-codec', module: 'commons-codec'
            exclude group: 'com.google.errorprone', module: 'error_prone_annotations'
            exclude group: 'com.google.guava', module: 'guava'
        }
        
        // Force compatible versions for Android
        implementation 'commons-codec:commons-codec:1.15'
        implementation 'com.google.guava:guava:31.1-android'
        implementation 'com.google.errorprone:error_prone_annotations:2.18.0'
        
        // Ensure compatible gRPC dependencies for the v2 client library
        implementation 'io.grpc:grpc-okhttp:1.71.0'
        implementation 'io.grpc:grpc-core:1.71.0'
        implementation 'io.grpc:grpc-stub:1.71.0'
        
        // Google Auth library for API key authentication and mTLS support
        implementation 'com.google.auth:google-auth-library-oauth2-http:1.39.0'
        implementation 'com.google.auth:google-auth-library-credentials:1.39.0'
        implementation 'com.google.auth:google-auth-library-appengine:1.39.0'
        
        implementation 'com.google.flogger:flogger:0.4'
        implementation 'com.google.flogger:flogger-system-backend:0.4'
        implementation 'com.google.protobuf:protobuf-java:3.22.3'
        implementation 'com.google.protobuf:protobuf-java-util:3.22.3'
        implementation 'joda-time:joda-time:2.9.2'
        implementation 'androidx.multidex:multidex:2.0.1'
        
        // Security library for encrypted shared preferences
        implementation 'androidx.security:security-crypto:1.1.0-alpha06'
    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()

            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
