package com.example.asl_flutter_input

import androidx.annotation.NonNull

import com.citrus.audio.MainActivity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.PluginRegistry
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.embedding.engine.FlutterEngine

/** AslFlutterInputPlugin */
class AslFlutterInputPlugin : FlutterPlugin, MethodCallHandler, ActivityAware {
    /// The MethodChannel that will the communication between Flutter and native Android
    ///
    /// This local reference serves to register the plugin with the Flutter Engine and unregister it
    /// when the Flutter Engine is detached from the Activity
    private lateinit var channel: MethodChannel

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "asl_flutter_input")
        channel.setMethodCallHandler(this)
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        if (call.method == "getPlatformVersion") {
            result.success("Android ${android.os.Build.VERSION.RELEASE}")
        } else {
            result.notImplemented()
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }


    override fun onAttachedToActivity(binding: ActivityPluginBinding) {
        val activity = binding.activity
        if (activity is FlutterActivity) {
            channel.setMethodCallHandler { call, result ->
                if (call.method == "setCurrentLanguage") {
                    try {
                        val languageCode = call.argument<String>("languageCode") ?: "en-US"
                        MainActivity.setCurrentLanguageCode(languageCode)
                        result.success(MainActivity.currentLanguageCode)
                    } catch (e: Exception) {
                        Log.e("Dev Log", "Exception: ${e.message}")
                        result.error("UNAVAILABLE", "Failed to set langauge", null)
                    }
                } else if (call.method == "setAPIKey") {
                    try {
//                        val apiKeyValue = call.argument<String>("apiKey") ?: ""
//                        MainActivity.setAPIKey(apiKeyValue)
                        result.success("MainActivity.API_KEY")
                    } catch (e: Exception) {
                        Log.e("Dev Log", "Exception: ${e.message}")
                        result.error("UNAVAILABLE", "Failed to set apikey", null)
                    }
                } else if (call.method == "initAudioTranscription") {
                    try {
                        val projectId = call.argument<String>("projectId") ?: ""
                        val serviceAccountJson = call.argument<String>("serviceAccountJson") ?: ""
                        MainActivity.configure(projectId, serviceAccountJson);
                        MainActivity.initAudioTranscription(activity);
                        result.success("Success")
                    } catch (e: Exception) {
                        Log.e("Dev Log", "Exception: ${e.message}")
                        result.error("UNAVAILABLE", "Failed to start MainActivity", null)
                    }
                } else if (call.method == "fetchTranscribedText") {
                    try {
                        result.success(MainActivity.transcribedText)
                    } catch (e: Exception) {
                        result.error("UNAVAILABLE", "fetchTranscribedText failed", null)
                    }
                } else if (call.method == "stopListening") {
                    try {
                        MainActivity.onDispose();
                    } catch (e: Exception) {
                        result.error("UNAVAILABLE", "fetchTranscribedText failed", null)
                    }
                } else {
                    result.notImplemented()
                }
            }
        } else {
            Log.e("Dev Log", "Activity is not a FlutterActivity")
        }
    }


    override fun onDetachedFromActivityForConfigChanges() {
        Log.d("Dev Log", "onDetachedFromActivityForConfigChanges")

    }

    override fun onReattachedToActivityForConfigChanges(p0: ActivityPluginBinding) {
        Log.d("Dev Log", "onReattachedToActivityForConfigChanges")

    }

    override fun onDetachedFromActivity() {
        Log.d("Dev Log", "onDetachedFromActivity")

    }
}
