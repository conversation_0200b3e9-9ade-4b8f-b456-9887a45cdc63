# ASL Audio Streaming to text

The ASL Audio Streaming to Text Flutter plugin offers real-time captioning.
It listens to live streaming audio and provides instant transcription. The plugin includes an Android library, integrated as an .aar file in the Android folder, featuring Android client libraries for interacting with Google's Cloud Speech API, similar to what is used in [Live Transcribe](https://support.google.com/accessibility/android/answer/9158064?hl=en).

The plugin provides access to the following features: 
1. Infinite streaming 
1. Supports [70+ langugages](https://cloud.google.com/speech-to-text/docs/speech-to-text-supported-languages) 
1. Robust to brief network loss (which occurs often when traveling and switching between network/wifi). Text is not lost, only delayed.

## Installation

To install this plugin,

1.  Clone this repo into the same parent folder where your flutter project is located at.

    `<NAME_EMAIL>:hirs/arabic-sign-language/asl-audio-streaming-to-text.git`

2.  Add generated .aar file to the android folder

    **asl-android-input**
    
    If we are changing the code in this repo then we should generate the 'aar' file and place it in the following repos:
    
    **a) asl-audio-streaming-to-text/asl-flutter-input(flutter plugin)**
        
    Copy the generated 'asl_android_input.aar' file into the plugin's android libs folder. Path is `/asl-audio-streaming-to-text/asl-flutter-input/android/libs`

    Add the following lines to the android's build.gradle file. Path is `asl_flutter_input/android/build.gradle`:

    ```
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs 'libs'
        }
    }

    dependencies {
        compileOnly fileTree(dir: 'libs', include: ['*.aar'])
    }
    ```


    Also, add the required dependencies from the android module's build.gradle into the plugin's android level build.gradle.

    Run the following command in the **asl-audio-streaming-to-text** after placing the aar file in the correct folder.
        
    ```
    /asl-audio-streaming-to-text>flutter clean
    /asl-audio-streaming-to-text>flutter pub get
    ```


    **b) asl-flutter-app(flutter project)**
        
    Copy the generated **asl_android_input.aar** file into the project's android libs folder. Path is `/asl-flutter-app/android/app/libs`

    Add the following lines to the android's build.gradle file. Path is `/asl_flutter_input/android/app/build.gradle`

    ```
    dependencies {
        implementation fileTree(dir: 'libs', include: ['*.aar'])
    }
    ```

    Add the following lines to android level build.gradle file. Path is `/asl_flutter_input/android/build.gradle:`

    ```
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs 'libs'
        }
    }
    ```

    Run the following command in the asl-flutter-app after placing the aar file in the correct folder.
    
    ```
    /asl-flutter-app>flutter clean
    /asl-flutter-app>flutter pub get
    ```


3.  In your flutter project's pubspec.yaml file under the dependencies section, include the plugin as follows:

        dependencies:
            flutter:
                sdk: flutter

            asl_flutter_input:
                path: ../asl-audio-streaming-to-text/

## Usage

To use this plugin, import the plugin in your dart file:

`import 'package:asl_flutter_input/asl_flutter_input.dart';`

Request the permission to access microphone:

```
await Permission.microphone.request();
// Install and import package 'permission_handler'
```

Create an instance of the class:

`final aslFlutterInputPlugin = AslFlutterInput();`

To change the language, invoke the method:

`await aslFlutterInputPlugin.setCurrentLanguage('ar-SA');`

The google's speech engine requires an API key. See the
[documentation on API keys](https://cloud.google.com/docs/authentication/api-keys) to learn more.
Set the API key using the method:

`await aslFlutterInputPlugin.setAPIKey(--your API key here--);`

Start the listening and audio transcription process by invoking the method:

`await aslFlutterInputPlugin.initAudioTranscription();`

The transcribed words can be obtained from:

`recognizedText = await aslFlutterInputPlugin.fetchTranscribedText();`
